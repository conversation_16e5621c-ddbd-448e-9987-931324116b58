<?php


include '../_base/base.php';

if ($_SESSION['user_role'] != 'admin') {
    exit;
}

$clients = $dbManager->findAll('clients');
$users = $dbManager->findAll('users');

include APP_PATH . '/layouts/head.php';

$scopingManager = new ScopingManager();

?>

<body>
<style>
    html, body {
        margin: 0;
        padding: 0;
    }

    .page-entry {
        width: 210mm;
        height: 297mm;
        background-color: #ccff99; /* dein <PERSON>rün */
        position: relative;
        page-break-after: always;
        margin: auto;
        padding:0;
    }

    .page-content {
        padding: 2cm;
        font-family: Arial, sans-serif;
    }

    @page {
        size: A4;
        margin: 0; /* versucht Ränder zu entfernen */
    }

    @media print {
        body {
            margin: 0;
            padding: 0;
        }

        .page-entry {
            width: 210mm;
            height: 297mm;
            background: #ccff99;
            page-break-after: always;
        }
    }

    .page-break {
        page-break-before: always;
        break-before: always;
    }
</style>

<div class="page">

    <div class="page-wrapper">

        <?php include APP_PATH . 'layouts/nav.php';  ?>

        <div class="page-header d-print-none">
            <div class="container-xl">
                <div class="row g-2 align-items-center">
                    <div class="col">
                        <!-- Page pre-title -->
                        <div class="page-pretitle">Scoping</div>
                        <h2 class="page-title">Alle Benutzer</h2>
                    </div>
                    <!-- Page title actions -->
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <form action="" method="post">
                                <input type="hidden" name="action" value="overlay">
                                <input type="hidden" name="overlay_template" value="add_user">
                                <input type="hidden" name="fields" value="">

                                <button type="submit" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2">
                                        <path d="M12 5l0 14"></path>
                                        <path d="M5 12l14 0"></path>
                                    </svg>
                                    Neuer Benutzer
                                </button>
                            </form>

                        </div>
                        <!-- BEGIN MODAL -->
                        <!-- END MODAL -->
                    </div>
                </div>
            </div>
        </div>

        <br class="d-print-none">
        <br class="d-print-none">

        <!-- PAGE ENTRIES -->



    </div>



</div>


<?php
include APP_PATH . 'layouts/footer.php';
include APP_PATH . 'layouts/scripts.php';
?>
</body>
</html>
