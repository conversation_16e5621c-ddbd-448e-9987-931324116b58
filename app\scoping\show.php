<?php


include '../_base/base.php';

if ($_SESSION['user_role'] != 'admin') {
    exit;
}

$clients = $dbManager->findAll('clients');
$users = $dbManager->findAll('users');

include APP_PATH . '/layouts/head.php';

$scopingManager = new ScopingManager();

// Get scopingId from URL parameter or use the first available scoping
$scopingId = isset($_GET['scoping_id']) ? intval($_GET['scoping_id']) : null;


// Get scoping and project data for use in JavaScript
$currentScoping = null;
$currentProject = null;
if (!empty($scopingId)) {
    $currentScoping = $scopingManager->findScopingById($scopingId);
    if (!empty($currentScoping['project_id'])) {
        $projectManager = new ProjectManager();
        $currentProject = $projectManager->findProjectById($currentScoping['project_id']);
    }
}

?>

<body>
<style>
    html, body {
        margin: 0;
        padding: 0;
    }

    .page-entry {
        width: 210mm;
        height: 297mm;
        background-color: #FFF; /* dein Grün */
        position: relative;
        page-break-after: always;
        margin: auto;
        padding:0;
        box-shadow: #CCC 0 0 10px;
    }

    .page-content {
        padding: 2cm 1cm 1cm 2cm;
        /*font-family: Arial, sans-serif;*/
    }

    .page-footer {
        position:absolute;
        right: 15px;
        bottom: 15px;
    }

    @page {
        size: A4;
        margin: 0; /* versucht Ränder zu entfernen */
    }

    @media print {
        body {
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: initial;
            print-color-adjust: initial;
        }

        .page-entry {
            width: 210mm;
            height: 297mm;
            page-break-after: always;
            margin: 0;
            padding: 0;
            box-shadow:none;
        }

        .page-entry:last-child {
            page-break-after: avoid;
        }
    }

    .page-break {
        page-break-before: always;
        break-before: always;
    }
</style>

<div class="page">

    <div class="page-wrapper">

        <?php include APP_PATH . 'layouts/nav.php';  ?>

        <div class="page-header d-print-none">
            <div class="container-xl">
                <div class="row g-2 align-items-center">
                    <div class="col">
                        <!-- Page pre-title -->
                        <div class="page-pretitle">Scoping</div>
                        <h2 class="page-title">
                            <?php if (!empty($currentProject['info_data']['name'])): ?>
                                <?php echo htmlspecialchars($currentProject['info_data']['name']); ?>
                                <?php if (!empty($scopingId)): ?>
                                    <small class="text-muted ms-1"> (ID: <?php echo $scopingId; ?>)</small>
                                <?php endif; ?>
                            <?php else: ?>
                                Scoping Dokument
                            <?php endif; ?>
                        </h2>
                    </div>
                    <!-- Page title actions -->
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <?php if (!empty($scopingId)): ?>
                                <!-- Print Button -->
                                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2">
                                        <polyline points="6,9 6,2 18,2 18,9"></polyline>
                                        <path d="M6,18H4a2,2 0 0,1-2-2V11a2,2 0 0,1,2-2H20a2,2 0 0,1,2,2v5a2,2 0 0,1-2,2H18"></path>
                                        <polyline points="6,14 6,22 18,22 18,14"></polyline>
                                    </svg>
                                    Drucken
                                </button>
                            <?php endif; ?>

                        </div>
                        <!-- BEGIN MODAL -->
                        <!-- END MODAL -->
                    </div>
                </div>
            </div>
        </div>

        <br class="d-print-none">
        <br class="d-print-none">

        <!-- PAGE ENTRIES -->
        <?php
        // Only render pages if we have a valid scopingId
        if (!empty($scopingId)) {
            // Get rendered scoping pages from templates
            $renderedPages = $scopingManager->getRenderedScopingPages($scopingId);

            // Echo each rendered page separated with page break
            foreach ($renderedPages as $index => $renderedPage) {
                echo $renderedPage;

                // Add page break between pages (except for the last one)
                if ($index < count($renderedPages) - 1) {
                    echo '<br class="d-print-none">';
                }
            }
        } else {
            echo '<div class="alert alert-warning">Kein Scoping gefunden. Bitte erstellen Sie zuerst ein Scoping.</div>';
        }
        ?>

    </div>



</div>


<?php
include APP_PATH . 'layouts/footer.php';
include APP_PATH . 'layouts/scripts.php';
?>

</body>
</html>
