<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry">
    <div class="page-content">
        <h1>Local SEO Analyse</h1>

        <?php if (!empty($projectData) && !empty($projectData['info_data']['name'])): ?>
            <h2>Unternehmen: <?php echo htmlspecialchars($projectData['info_data']['name']); ?></h2>
        <?php endif; ?>

        <?php if (!empty($projectData['website_url'])): ?>
            <p><strong>Website:</strong> <?php echo htmlspecialchars($projectData['website_url']); ?></p>
        <?php endif; ?>

        <?php if (!empty($projectData) && !empty($projectData['info_data'])): ?>
            <h3>Standortdaten:</h3>
            <ul>
                <?php if (!empty($projectData['info_data']['street'])): ?>
                    <li><strong>Adresse:</strong> <?php echo htmlspecialchars($projectData['info_data']['street']); ?></li>
                <?php endif; ?>
                <?php if (!empty($projectData['info_data']['zip']) && !empty($projectData['info_data']['city'])): ?>
                    <li><strong>PLZ/Ort:</strong> <?php echo htmlspecialchars($projectData['info_data']['zip'] . ' ' . $projectData['info_data']['city']); ?></li>
                <?php endif; ?>
                <?php if (!empty($projectData['info_data']['phone'])): ?>
                    <li><strong>Telefon:</strong> <?php echo htmlspecialchars($projectData['info_data']['phone']); ?></li>
                <?php endif; ?>
            </ul>
        <?php endif; ?>

        <h3>Scoping Details:</h3>
        <?php if (!empty($scopingData['options_data'])): ?>
            <?php $options = json_decode($scopingData['options_data'], true); ?>
            <?php if (!empty($options)): ?>
                <ul>
                    <?php foreach ($options as $key => $value): ?>
                        <li><strong><?php echo htmlspecialchars($key); ?>:</strong> <?php echo htmlspecialchars($value); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        <?php endif; ?>

        <p>Hier können weitere Local SEO spezifische Inhalte eingefügt werden.</p>
    </div>
    <div class="page-footer">Seite 2</div>
</div>
