<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry">
    <div class="page-content">
        <h1>Local SEO Analyse</h1>

        <?php if (!empty($projectData) && !empty($projectData['info_data']['name'])): ?>
            <h2>Unternehmen: <?php echo htmlspecialchars($projectData['info_data']['name']); ?></h2>
        <?php endif; ?>

        <?php if (!empty($projectData['website_url'])): ?>
            <p><strong>Website:</strong> <?php echo htmlspecialchars($projectData['website_url']); ?></p>
        <?php endif; ?>

        <?php if (!empty($projectData) && !empty($projectData['info_data'])): ?>
            <h3>Standortdaten:</h3>
            <ul>
                <?php if (!empty($projectData['info_data']['street'])): ?>
                    <li><strong>Adresse:</strong> <?php echo htmlspecialchars($projectData['info_data']['street']); ?></li>
                <?php endif; ?>
                <?php if (!empty($projectData['info_data']['zip']) && !empty($projectData['info_data']['city'])): ?>
                    <li><strong>PLZ/Ort:</strong> <?php echo htmlspecialchars($projectData['info_data']['zip'] . ' ' . $projectData['info_data']['city']); ?></li>
                <?php endif; ?>
                <?php if (!empty($projectData['info_data']['phone'])): ?>
                    <li><strong>Telefon:</strong> <?php echo htmlspecialchars($projectData['info_data']['phone']); ?></li>
                <?php endif; ?>
            </ul>
        <?php endif; ?>

        <h3>Scoping Details:</h3>
        <?php if (!empty($scopingData['options_data'])): ?>
            <?php $options = json_decode($scopingData['options_data'], true); ?>
            <?php if (!empty($options)): ?>
                <ul>
                    <?php foreach ($options as $key => $value): ?>
                        <li><strong><?php echo htmlspecialchars($key); ?>:</strong> <?php echo htmlspecialchars($value); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        <?php endif; ?>

        <h3>Local SEO Ranking Positionen</h3>
        <div id="local-seo-map" style="width: 100%; height: 400px; border: 1px solid #ccc; margin: 20px 0;"></div>

        <style>
            /* Print-friendly styles for the map */
            @media print {
                #local-seo-map {
                    height: 300px !important;
                    page-break-inside: avoid;
                }

                .ranking-legend {
                    page-break-inside: avoid;
                }

                /* Ensure map content is visible in print */
                #local-seo-map * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
            }
        </style>

        <div class="ranking-legend" style="margin: 10px 0;">
            <h4>Legende:</h4>
            <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                <div style="display: flex; align-items: center;">
                    <div style="width: 12px; height: 12px; background-color: #00ff00; border-radius: 50%; margin-right: 5px;"></div>
                    <span>Position 1-2 (Sehr gut)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 12px; height: 12px; background-color: #80ff00; border-radius: 50%; margin-right: 5px;"></div>
                    <span>Position 3-4 (Gut)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 12px; height: 12px; background-color: #ffff00; border-radius: 50%; margin-right: 5px;"></div>
                    <span>Position 5-6 (Mittel)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 12px; height: 12px; background-color: #ff8000; border-radius: 50%; margin-right: 5px;"></div>
                    <span>Position 7-8 (Schlecht)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 12px; height: 12px; background-color: #ff0000; border-radius: 50%; margin-right: 5px;"></div>
                    <span>Position 9-10 (Sehr schlecht)</span>
                </div>
            </div>
        </div>

        <p>Die Karte zeigt die Local SEO Ranking-Positionen an verschiedenen geografischen Punkten rund um den Unternehmensstandort.</p>
    </div>
    <div class="page-footer">Seite 2</div>
</div>

<script>
// Initialize Google Map for Local SEO analysis
function initLocalSeoMap() {
    // Sample data - in a real implementation, this would come from the scoping data
    const mapData = {
        center: { lat: 50.1109, lng: 8.6821 }, // Frankfurt am Main as default
        points: [
            { lat: 50.1109, lng: 8.6821, position: 3, label: "Zentrum" },
            { lat: 50.1209, lng: 8.6921, position: 1, label: "Nord" },
            { lat: 50.1009, lng: 8.6721, position: 7, label: "Süd" },
            { lat: 50.1109, lng: 8.6921, position: 5, label: "Ost" },
            { lat: 50.1109, lng: 8.6721, position: 9, label: "West" }
        ]
    };

    // Try to get real data from scoping if available
    <?php if (!empty($scopingData) && !empty($projectData)): ?>
        // Override with real project location if available
        <?php if (!empty($projectData['info_data']['latitude']) && !empty($projectData['info_data']['longitude'])): ?>
            mapData.center = {
                lat: <?php echo floatval($projectData['info_data']['latitude']); ?>,
                lng: <?php echo floatval($projectData['info_data']['longitude']); ?>
            };

            // Generate points around the business location
            const centerLat = <?php echo floatval($projectData['info_data']['latitude']); ?>;
            const centerLng = <?php echo floatval($projectData['info_data']['longitude']); ?>;
            const offset = 0.01; // Approximately 1km

            mapData.points = [
                { lat: centerLat, lng: centerLng, position: 3, label: "Geschäftsstandort" },
                { lat: centerLat + offset, lng: centerLng, position: 1, label: "Nord (1km)" },
                { lat: centerLat - offset, lng: centerLng, position: 7, label: "Süd (1km)" },
                { lat: centerLat, lng: centerLng + offset, position: 5, label: "Ost (1km)" },
                { lat: centerLat, lng: centerLng - offset, position: 9, label: "West (1km)" }
            ];
        <?php endif; ?>
    <?php endif; ?>

    // Function to get color based on ranking position
    function getMarkerColor(position) {
        if (position <= 2) return '#00ff00'; // Green
        if (position <= 4) return '#80ff00'; // Light green
        if (position <= 6) return '#ffff00'; // Yellow
        if (position <= 8) return '#ff8000'; // Orange
        return '#ff0000'; // Red
    }

    // Create map
    const map = new google.maps.Map(document.getElementById('local-seo-map'), {
        zoom: 13,
        center: mapData.center,
        mapTypeId: 'roadmap'
    });

    // Add markers for each point
    mapData.points.forEach((point, index) => {
        const marker = new google.maps.Marker({
            position: { lat: point.lat, lng: point.lng },
            map: map,
            title: `${point.label} - Position ${point.position}`,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 10,
                fillColor: getMarkerColor(point.position),
                fillOpacity: 0.8,
                strokeColor: '#000000',
                strokeWeight: 2
            }
        });

        // Add info window
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h4>${point.label}</h4>
                    <p><strong>Ranking Position:</strong> ${point.position}</p>
                    <p><strong>Koordinaten:</strong> ${point.lat.toFixed(4)}, ${point.lng.toFixed(4)}</p>
                </div>
            `
        });

        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    });

    // Add a circle to show the general area
    const circle = new google.maps.Circle({
        strokeColor: '#0066cc',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#0066cc',
        fillOpacity: 0.1,
        map: map,
        center: mapData.center,
        radius: 2000 // 2km radius
    });
}

// Load Google Maps API if not already loaded
function loadGoogleMapsAPI() {
    if (typeof google !== 'undefined' && google.maps) {
        initLocalSeoMap();
        return;
    }

    // Check if script is already being loaded
    if (document.querySelector('script[src*="maps.googleapis.com"]')) {
        // Wait for it to load
        const checkGoogleMaps = setInterval(() => {
            if (typeof google !== 'undefined' && google.maps) {
                clearInterval(checkGoogleMaps);
                initLocalSeoMap();
            }
        }, 100);
        return;
    }

    // Load Google Maps API
    const script = document.createElement('script');

    // Try to get API key from configuration
    let apiKey = '';
    <?php
    // Try to get Google Maps API key from various sources
    $googleMapsApiKey = '';

    // Check if there's a config file or environment variable
    if (defined('GOOGLE_MAPS_API_KEY')) {
        $googleMapsApiKey = GOOGLE_MAPS_API_KEY;
    } elseif (isset($_ENV['GOOGLE_MAPS_API_KEY'])) {
        $googleMapsApiKey = $_ENV['GOOGLE_MAPS_API_KEY'];
    } elseif (file_exists(APP_PATH . '../config/google_maps.php')) {
        $config = include APP_PATH . '../config/google_maps.php';
        $googleMapsApiKey = $config['api_key'] ?? '';
    }

    echo "apiKey = " . json_encode($googleMapsApiKey) . ";";
    ?>

    if (apiKey) {
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initLocalSeoMap`;
    } else {
        script.src = 'https://maps.googleapis.com/maps/api/js?callback=initLocalSeoMap';
    }

    script.async = true;
    script.defer = true;

    // Fallback if no API key is available
    script.onerror = function() {
        console.warn('Google Maps API could not be loaded. Please add a valid API key.');
        showStaticMapFallback();
    };

    // Show static map fallback
    function showStaticMapFallback() {
        const mapContainer = document.getElementById('local-seo-map');
        mapContainer.innerHTML = `
            <div style="position: relative; height: 400px; background: linear-gradient(45deg, #e8f4f8 25%, transparent 25%), linear-gradient(-45deg, #e8f4f8 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e8f4f8 75%), linear-gradient(-45deg, transparent 75%, #e8f4f8 75%); background-size: 20px 20px; background-position: 0 0, 0 10px, 10px -10px, -10px 0px; border: 1px solid #ddd;">
                <div style="position: absolute; top: 10px; left: 10px; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                    <h4 style="margin: 0 0 10px 0; color: #333;">Local SEO Ranking Positionen</h4>
                    <div style="font-size: 12px; color: #666;">
                        <div style="margin: 5px 0;">📍 Zentrum: Position 3 <span style="color: #ffff00;">●</span></div>
                        <div style="margin: 5px 0;">📍 Nord: Position 1 <span style="color: #00ff00;">●</span></div>
                        <div style="margin: 5px 0;">📍 Süd: Position 7 <span style="color: #ff8000;">●</span></div>
                        <div style="margin: 5px 0;">📍 Ost: Position 5 <span style="color: #ffff00;">●</span></div>
                        <div style="margin: 5px 0;">📍 West: Position 9 <span style="color: #ff0000;">●</span></div>
                    </div>
                </div>

                <!-- Simulated map points -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 20px; height: 20px; background: #ffff00; border: 2px solid #000; border-radius: 50%; z-index: 10;" title="Zentrum - Position 3"></div>
                <div style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); width: 16px; height: 16px; background: #00ff00; border: 2px solid #000; border-radius: 50%; z-index: 10;" title="Nord - Position 1"></div>
                <div style="position: absolute; top: 70%; left: 50%; transform: translate(-50%, -50%); width: 16px; height: 16px; background: #ff8000; border: 2px solid #000; border-radius: 50%; z-index: 10;" title="Süd - Position 7"></div>
                <div style="position: absolute; top: 50%; left: 70%; transform: translate(-50%, -50%); width: 16px; height: 16px; background: #ffff00; border: 2px solid #000; border-radius: 50%; z-index: 10;" title="Ost - Position 5"></div>
                <div style="position: absolute; top: 50%; left: 30%; transform: translate(-50%, -50%); width: 16px; height: 16px; background: #ff0000; border: 2px solid #000; border-radius: 50%; z-index: 10;" title="West - Position 9"></div>

                <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 5px 10px; border-radius: 3px; font-size: 11px; color: #666;">
                    Statische Kartenansicht<br>
                    <small>Für interaktive Karte Google Maps API konfigurieren</small>
                </div>
            </div>
        `;
    }

    document.head.appendChild(script);
}

// Initialize map when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadGoogleMapsAPI);
} else {
    loadGoogleMapsAPI();
}
</script>
